Categories:
  - Science & Education
License: GPL-3.0-or-later
AuthorName: Ahmet Çetinkaya
AuthorEmail: <EMAIL>
WebSite: https://whph.ahmetcetinkaya.me
SourceCode: https://github.com/ahmet-cetinkaya/whph
IssueTracker: https://github.com/ahmet-cetinkaya/whph/issues
Donate: https://ahmetcetinkaya.me/donate

AutoName: Work Hard Play Hard

RepoType: git
Repo: https://github.com/ahmet-cetinkaya/whph
Binaries: https://github.com/ahmet-cetinkaya/whph/releases/download/v%v/whph-v%v-android.apk

Builds:
  - versionName: 0.9.9
    versionCode: 46
    commit: d1a4118e28b3fd27ea8876ea68eee8521752f5d4
    submodules: true
    output: build/app/outputs/flutter-apk/app-release.apk
    srclibs:
      - flutter@stable
    rm:
      - ios
      - linux
      - macos
      - web
      - windows
    prebuild:
      - flutterVersion=$(bash scripts/get_flutter_version.sh)
      - '[[ $flutterVersion ]] || exit 1'
      - git -C $$flutter$$ checkout -f $flutterVersion
      - export PUB_CACHE=$(pwd)/.pub-cache
      - $$flutter$$/bin/flutter config --no-analytics
      - $$flutter$$/bin/flutter packages pub get
    scandelete:
      - android
      - .pub-cache
    build:
      - export PUB_CACHE=$(pwd)/.pub-cache
      - $$flutter$$/bin/flutter build apk --release --split-debug-info=build/app/outputs/symbols
        --obfuscate --tree-shake-icons

AllowedAPKSigningKeys: 4b0de165375bb1179fbee37fbd70de03813284529e0b0c5d3ce5e794f03aa0ae

AutoUpdateMode: Version
UpdateCheckMode: Tags
UpdateCheckData: pubspec.yaml|version:\s.+\+(\d+)|.|version:\s(.+)\+
CurrentVersion: 0.9.9+46
CurrentVersionCode: 46
