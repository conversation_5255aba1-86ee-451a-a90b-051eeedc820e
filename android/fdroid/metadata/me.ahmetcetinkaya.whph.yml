Categories:
  - Science & Education
License: GPL-3.0-or-later
AuthorName: Ahmet Çetinkaya
AuthorEmail: <EMAIL>
WebSite: https://whph.ahmetcetinkaya.me
SourceCode: https://github.com/ahmet-cetinkaya/whph
IssueTracker: https://github.com/ahmet-cetinkaya/whph/issues
Donate: https://ahmetcetinkaya.me/donate

AutoName: Work Hard Play Hard

RepoType: git
Repo: https://github.com/ahmet-cetinkaya/whph

Builds:
  - versionName: 0.9.9
    versionCode: 461
    commit: bc9e5ce0bedd948cd4330fe016a663c5bf4e34c6
    submodules: true
    output: build/app/outputs/flutter-apk/app-x86_64-release.apk
    srclibs:
      - flutter@stable
    rm:
      - ios
      - linux
      - macos
      - web
      - windows
    prebuild:
      - flutterVersion=$(bash scripts/get_flutter_version.sh)
      - '[[ $flutterVersion ]] || exit 1'
      - git -C $$flutter$$ checkout -f $flutterVersion
      - export PUB_CACHE=$(pwd)/.pub-cache
      - $$flutter$$/bin/flutter config --no-analytics
      - $$flutter$$/bin/flutter packages pub get
      - echo "# F-Droid specific ProGuard rules to exclude Google Play dependencies" > android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.** { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.core.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.gms.** { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.gms.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallSessionState { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallManager { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.tasks.OnFailureListener { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.splitcompat.SplitCompatApplication { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.tasks.OnSuccessListener { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.core.splitinstall.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.core.tasks.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.core.splitcompat.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.** { *; }" >> android/app/proguard-rules-fdroid.pro
      - sed -i "s/proguard-rules.pro'/proguard-rules.pro', 'proguard-rules-fdroid.pro'/" android/app/build.gradle
      - 'echo "configurations.all { exclude group: \"com.google.android.play\", module: \"core\" }" >> android/app/build.gradle'
      - 'echo "configurations.all { exclude group: \"com.google.android.gms\" }" >> android/app/build.gradle'
    scandelete:
      - android
      - .pub-cache
    build:
      - export PUB_CACHE=$(pwd)/.pub-cache
      - export VERSION_CODE=461
      - $$flutter$$/bin/flutter build apk --release --split-per-abi --target-platform="android-x64"
        --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons

  - versionName: 0.9.9
    versionCode: 462
    commit: bc9e5ce0bedd948cd4330fe016a663c5bf4e34c6
    submodules: true
    output: build/app/outputs/flutter-apk/app-armeabi-v7a-release.apk
    srclibs:
      - flutter@stable
    rm:
      - ios
      - linux
      - macos
      - web
      - windows
    prebuild:
      - flutterVersion=$(bash scripts/get_flutter_version.sh)
      - '[[ $flutterVersion ]] || exit 1'
      - git -C $$flutter$$ checkout -f $flutterVersion
      - export PUB_CACHE=$(pwd)/.pub-cache
      - $$flutter$$/bin/flutter config --no-analytics
      - $$flutter$$/bin/flutter packages pub get
      - echo "# F-Droid specific ProGuard rules to exclude Google Play dependencies" > android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.** { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.core.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.gms.** { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.gms.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallSessionState { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallManager { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.tasks.OnFailureListener { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.splitcompat.SplitCompatApplication { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.tasks.OnSuccessListener { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.core.splitinstall.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.core.tasks.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.core.splitcompat.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.** { *; }" >> android/app/proguard-rules-fdroid.pro
      - sed -i "s/proguard-rules.pro'/proguard-rules.pro', 'proguard-rules-fdroid.pro'/" android/app/build.gradle
      - 'echo "configurations.all { exclude group: \"com.google.android.play\", module: \"core\" }" >> android/app/build.gradle'
      - 'echo "configurations.all { exclude group: \"com.google.android.gms\" }" >> android/app/build.gradle'
    scandelete:
      - android
      - .pub-cache
    build:
      - export PUB_CACHE=$(pwd)/.pub-cache
      - export VERSION_CODE=462
      - $$flutter$$/bin/flutter build apk --release --split-per-abi --target-platform="android-arm"
        --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons

  - versionName: 0.9.9
    versionCode: 463
    commit: bc9e5ce0bedd948cd4330fe016a663c5bf4e34c6
    submodules: true
    output: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
    srclibs:
      - flutter@stable
    rm:
      - ios
      - linux
      - macos
      - web
      - windows
    prebuild:
      - flutterVersion=$(bash scripts/get_flutter_version.sh)
      - '[[ $flutterVersion ]] || exit 1'
      - git -C $$flutter$$ checkout -f $flutterVersion
      - export PUB_CACHE=$(pwd)/.pub-cache
      - $$flutter$$/bin/flutter config --no-analytics
      - $$flutter$$/bin/flutter packages pub get
      - echo "# F-Droid specific ProGuard rules to exclude Google Play dependencies" > android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.** { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.core.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.gms.** { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.gms.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallSessionState { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallManager { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.tasks.OnFailureListener { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.splitcompat.SplitCompatApplication { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.core.tasks.OnSuccessListener { *; }" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.core.splitinstall.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.core.tasks.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.core.splitcompat.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-dontwarn com.google.android.play.**" >> android/app/proguard-rules-fdroid.pro
      - echo "-assumenosideeffects class com.google.android.play.** { *; }" >> android/app/proguard-rules-fdroid.pro
      - sed -i "s/proguard-rules.pro'/proguard-rules.pro', 'proguard-rules-fdroid.pro'/" android/app/build.gradle
      - echo "configurations.all { exclude group: 'com.google.android.play', module: 'core' }" >> android/app/build.gradle
      - echo "configurations.all { exclude group: 'com.google.android.gms' }" >> android/app/build.gradle
    scandelete:
      - android
      - .pub-cache
    build:
      - export PUB_CACHE=$(pwd)/.pub-cache
      - export VERSION_CODE=463
      - $$flutter$$/bin/flutter build apk --release --split-per-abi --target-platform="android-arm64"
        --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons

AllowedAPKSigningKeys: 4b0de165375bb1179fbee37fbd70de03813284529e0b0c5d3ce5e794f03aa0ae

AutoUpdateMode: Version
UpdateCheckMode: Tags
UpdateCheckData: pubspec.yaml|version:\s.+\+(\d+)|.|version:\s(.+)\+
CurrentVersion: 0.9.9+463
CurrentVersionCode: 463
