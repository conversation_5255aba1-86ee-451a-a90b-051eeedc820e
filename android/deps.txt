Configuration on demand is an incubating feature.

> Configure project :gradle
WARNING: Unsupported Kotlin plugin version.
The `embedded-kotlin` and `kotlin-dsl` plugins rely on features of Kotlin `1.9.10` that might work differently than in the requested version `1.9.20`.

> Task :gradle:checkKotlinGradlePluginConfigurationErrors
> Task :gradle:compileKotlin UP-TO-DATE
> Task :gradle:compileJava NO-SOURCE
> Task :gradle:compileGroovy NO-SOURCE
> Task :gradle:pluginDescriptors UP-TO-DATE
> Task :gradle:processResources UP-TO-DATE
> Task :gradle:classes UP-TO-DATE
> Task :gradle:jar UP-TO-DATE

> Task :app:dependencies

------------------------------------------------------------
Project ':app'
------------------------------------------------------------

releaseRuntimeClasspath - Runtime classpath of /release.
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.0 -> 1.9.24
|    |    +--- org.jetbrains:annotations:13.0 -> 23.0.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 1.9.10 (c)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 1.9.0 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0 -> 1.9.10
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.10 -> 1.9.24 (*)
+--- androidx.core:core-ktx:1.12.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.9.1
|    |    \--- androidx.annotation:annotation-jvm:1.9.1
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.24 (*)
|    +--- androidx.core:core:1.12.0 -> 1.15.0
|    |    +--- androidx.annotation:annotation:1.8.1 -> 1.9.1 (*)
|    |    +--- androidx.annotation:annotation-experimental:1.4.1
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 1.9.24 (*)
|    |    +--- androidx.collection:collection:1.4.2
|    |    |    \--- androidx.collection:collection-jvm:1.4.2
|    |    |         +--- androidx.annotation:annotation:1.8.1 -> 1.9.1 (*)
|    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    |         +--- androidx.collection:collection-ktx:1.4.2 (c)
|    |    |         \--- androidx.collection:collection-ktx:1.3.0 -> 1.4.2 (c)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|    |    |    \--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.interpolator:interpolator:1.0.0
|    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 -> 2.8.7
|    |    |    \--- androidx.lifecycle:lifecycle-runtime-android:2.8.7
|    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.9.1 (*)
|    |    |         +--- androidx.arch.core:core-common:2.2.0
|    |    |         |    \--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|    |    |         +--- androidx.arch.core:core-runtime:2.2.0
|    |    |         |    +--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|    |    |         |    \--- androidx.arch.core:core-common:2.2.0 (*)
|    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7
|    |    |         |    \--- androidx.lifecycle:lifecycle-common-jvm:2.8.7
|    |    |         |         +--- androidx.annotation:annotation:1.8.1 -> 1.9.1 (*)
|    |    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3
|    |    |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3
|    |    |         |         |         +--- org.jetbrains:annotations:23.0.0
|    |    |         |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.7.3
|    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 (c)
|    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3 (c)
|    |    |         |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 (c)
|    |    |         |         |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20 -> 1.9.0 (*)
|    |    |         |         +--- androidx.lifecycle:lifecycle-common-java8:2.8.7 (c)
|    |    |         |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |         |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|    |    |         |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |         |         +--- androidx.lifecycle:lifecycle-runtime-ktx:2.8.7 (c)
|    |    |         |         +--- androidx.lifecycle:lifecycle-service:2.8.7 (c)
|    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7 (c)
|    |    |         |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |         +--- androidx.profileinstaller:profileinstaller:1.3.1
|    |    |         |    +--- androidx.annotation:annotation:1.2.0 -> 1.9.1 (*)
|    |    |         |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    |         |    +--- androidx.startup:startup-runtime:1.1.1
|    |    |         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|    |    |         |    |    \--- androidx.tracing:tracing:1.0.0 -> 1.2.0
|    |    |         |    |         \--- androidx.annotation:annotation:1.2.0 -> 1.9.1 (*)
|    |    |         |    \--- com.google.guava:listenablefuture:1.0
|    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3
|    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 (*)
|    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.7.3 (*)
|    |    |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20 -> 1.9.0 (*)
|    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |         +--- androidx.lifecycle:lifecycle-common-java8:2.8.7 (c)
|    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|    |    |         +--- androidx.lifecycle:lifecycle-service:2.8.7 (c)
|    |    |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7 (c)
|    |    |         \--- androidx.lifecycle:lifecycle-runtime-ktx:2.8.7 (c)
|    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|    |    |    \--- androidx.collection:collection:1.0.0 -> 1.4.2 (*)
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    \--- androidx.core:core-ktx:1.15.0 -> 1.12.0 (c)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    \--- androidx.core:core:1.12.0 -> 1.15.0 (c)
+--- androidx.work:work-runtime-ktx:2.9.0
|    +--- androidx.work:work-runtime:2.9.0
|    |    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.4.1 (*)
|    |    +--- androidx.core:core:1.9.0 -> 1.15.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata:2.5.1 -> 2.8.7
|    |    |    +--- androidx.arch.core:core-common:2.2.0 (*)
|    |    |    +--- androidx.arch.core:core-runtime:2.2.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7
|    |    |    |    +--- androidx.arch.core:core-common:2.2.0 (*)
|    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common-java8:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-service:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-runtime-ktx:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common-java8:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-service:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-common-java8:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-service:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    \--- androidx.lifecycle:lifecycle-runtime-ktx:2.8.7 (c)
|    |    +--- androidx.lifecycle:lifecycle-service:2.5.1 -> 2.8.7
|    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-common-java8:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    \--- androidx.lifecycle:lifecycle-runtime-ktx:2.8.7 (c)
|    |    +--- androidx.room:room-ktx:2.5.0
|    |    |    +--- androidx.room:room-common:2.5.0
|    |    |    |    +--- androidx.annotation:annotation:1.3.0 -> 1.9.1 (*)
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.20 -> 1.9.0 (*)
|    |    |    +--- androidx.room:room-runtime:2.5.0
|    |    |    |    +--- androidx.annotation:annotation-experimental:1.1.0 -> 1.4.1 (*)
|    |    |    |    +--- androidx.arch.core:core-runtime:2.0.1 -> 2.2.0 (*)
|    |    |    |    +--- androidx.room:room-common:2.5.0 (*)
|    |    |    |    +--- androidx.sqlite:sqlite:2.3.0
|    |    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.20 -> 1.9.24 (*)
|    |    |    |    \--- androidx.sqlite:sqlite-framework:2.3.0
|    |    |    |         +--- androidx.annotation:annotation:1.2.0 -> 1.9.1 (*)
|    |    |    |         +--- androidx.sqlite:sqlite:2.3.0 (*)
|    |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.20 -> 1.9.24 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.20 -> 1.9.24 (*)
|    |    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 -> 1.7.3 (*)
|    |    +--- androidx.sqlite:sqlite-framework:2.3.0 (*)
|    |    +--- androidx.startup:startup-runtime:1.1.1 (*)
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1 -> 1.7.3 (*)
|    |    \--- androidx.work:work-runtime-ktx:2.9.0 (c)
|    \--- androidx.work:work-runtime:2.9.0 (c)
+--- io.flutter:armeabi_v7a_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7
+--- io.flutter:arm64_v8a_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7
+--- io.flutter:x86_64_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7
+--- project :android_intent_plus
|    +--- androidx.annotation:annotation:1.8.2 -> 1.9.1 (*)
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7
|         +--- androidx.lifecycle:lifecycle-common:2.7.0 -> 2.8.7 (*)
|         +--- androidx.lifecycle:lifecycle-common-java8:2.7.0 -> 2.8.7
|         |    +--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|         |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|         |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-service:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    \--- androidx.lifecycle:lifecycle-runtime-ktx:2.8.7 (c)
|         +--- androidx.lifecycle:lifecycle-process:2.7.0 -> 2.8.7
|         |    +--- androidx.annotation:annotation:1.2.0 -> 1.9.1 (*)
|         |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (*)
|         |    +--- androidx.startup:startup-runtime:1.1.1 (*)
|         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|         |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-common-java8:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-service:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7 (c)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    \--- androidx.lifecycle:lifecycle-runtime-ktx:2.8.7 (c)
|         +--- androidx.lifecycle:lifecycle-runtime:2.7.0 -> 2.8.7 (*)
|         +--- androidx.fragment:fragment:1.7.1
|         |    +--- androidx.activity:activity:1.8.1
|         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|         |    |    +--- androidx.collection:collection:1.0.0 -> 1.4.2 (*)
|         |    |    +--- androidx.core:core:1.8.0 -> 1.15.0 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 -> 2.8.7
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-android:2.8.7
|         |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.9.1 (*)
|         |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|         |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 (*)
|         |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 (*)
|         |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-common-java8:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-runtime-ktx:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-service:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7 (c)
|         |    |    |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 -> 2.8.7
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|         |    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.12.0 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (*)
|         |    |    |    +--- androidx.savedstate:savedstate:1.2.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|         |    |    |    |    +--- androidx.arch.core:core-common:2.1.0 -> 2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 -> 2.8.7 (*)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|         |    |    |    |    \--- androidx.savedstate:savedstate-ktx:1.2.1 (c)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|         |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-common-java8:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-service:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-runtime-ktx:2.8.7 (c)
|         |    |    +--- androidx.profileinstaller:profileinstaller:1.3.0 -> 1.3.1 (*)
|         |    |    +--- androidx.savedstate:savedstate:1.2.1 (*)
|         |    |    +--- androidx.tracing:tracing:1.0.0 -> 1.2.0 (*)
|         |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|         |    |    \--- androidx.activity:activity-ktx:1.8.1 (c)
|         |    +--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|         |    +--- androidx.annotation:annotation-experimental:1.4.0 -> 1.4.1 (*)
|         |    +--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|         |    +--- androidx.core:core-ktx:1.2.0 -> 1.12.0 (*)
|         |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 -> 2.8.7 (*)
|         |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 -> 2.8.7 (*)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 -> 2.8.7 (*)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 -> 2.8.7 (*)
|         |    +--- androidx.loader:loader:1.0.0
|         |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|         |    |    +--- androidx.core:core:1.0.0 -> 1.15.0 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.8.7 (*)
|         |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.8.7 (*)
|         |    +--- androidx.profileinstaller:profileinstaller:1.3.1 (*)
|         |    +--- androidx.savedstate:savedstate:1.2.1 (*)
|         |    +--- androidx.viewpager:viewpager:1.0.0
|         |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|         |    |    +--- androidx.core:core:1.0.0 -> 1.15.0 (*)
|         |    |    \--- androidx.customview:customview:1.0.0 -> 1.1.0
|         |    |         +--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|         |    |         +--- androidx.core:core:1.3.0 -> 1.15.0 (*)
|         |    |         \--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|         |    \--- androidx.fragment:fragment-ktx:1.7.1 (c)
|         +--- androidx.annotation:annotation:1.8.0 -> 1.9.1 (*)
|         +--- androidx.tracing:tracing:1.2.0 (*)
|         +--- androidx.core:core:1.13.1 -> 1.15.0 (*)
|         +--- androidx.window:window-java:1.2.0
|         |    +--- androidx.core:core:1.3.2 -> 1.15.0 (*)
|         |    +--- androidx.window:window:1.2.0
|         |    |    +--- androidx.annotation:annotation:1.3.0 -> 1.9.1 (*)
|         |    |    +--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|         |    |    +--- androidx.core:core:1.8.0 -> 1.15.0 (*)
|         |    |    +--- androidx.window.extensions.core:core:1.0.0
|         |    |    |    +--- androidx.annotation:annotation:1.6.0 -> 1.9.1 (*)
|         |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.20 -> 1.9.24 (*)
|         |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|         |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1 -> 1.7.3 (*)
|         |    |    \--- androidx.window:window-java:1.2.0 (c)
|         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.1 -> 1.7.3 (*)
|         |    \--- androidx.window:window:1.2.0 (c)
|         \--- com.getkeepsafe.relinker:relinker:1.4.5
+--- project :app_usage
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0 (*)
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :audioplayers_android
|    +--- androidx.core:core-ktx:1.9.0 -> 1.12.0 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10 -> 1.9.10 (*)
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4 -> 1.7.3 (*)
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 -> 1.7.3 (*)
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :device_info_plus
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.22 -> 1.9.24 (*)
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :file_picker
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0 (*)
|    +--- androidx.core:core:1.15.0 (*)
|    +--- androidx.annotation:annotation:1.9.1 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (*)
|    +--- org.apache.tika:tika-core:3.1.0
|    |    +--- org.slf4j:slf4j-api:2.0.16
|    |    \--- commons-io:commons-io:2.18.0
|    +--- androidx.core:core-ktx:1.15.0 -> 1.12.0 (*)
|    +--- project :flutter_plugin_android_lifecycle
|    |    +--- androidx.annotation:annotation:1.9.1 (*)
|    |    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :file_saver
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4 -> 1.7.3 (*)
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 -> 1.7.3 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10 (*)
|    +--- androidx.annotation:annotation:1.7.0 -> 1.9.1 (*)
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :flutter_local_notifications
|    +--- androidx.core:core:1.3.0 -> 1.15.0 (*)
|    +--- androidx.media:media:1.1.0
|    |    +--- androidx.core:core:1.1.0 -> 1.15.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.4.2 (*)
|    +--- com.google.code.gson:gson:2.12.0
|    |    \--- com.google.errorprone:error_prone_annotations:2.36.0
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :flutter_plugin_android_lifecycle (*)
+--- project :network_info_plus
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.22 -> 1.9.24 (*)
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :package_info_plus
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.22 -> 1.9.24 (*)
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :path_provider_android
|    +--- androidx.annotation:annotation:1.9.1 (*)
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :permission_handler_android
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :qr_code_scanner_plus
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0 -> 1.9.10 (*)
|    +--- com.journeyapps:zxing-android-embedded:4.3.0
|    +--- androidx.appcompat:appcompat:1.6.1
|    |    +--- androidx.activity:activity:1.6.0 -> 1.8.1 (*)
|    |    +--- androidx.annotation:annotation:1.3.0 -> 1.9.1 (*)
|    |    +--- androidx.appcompat:appcompat-resources:1.6.1
|    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.9.1 (*)
|    |    |    +--- androidx.collection:collection:1.0.0 -> 1.4.2 (*)
|    |    |    +--- androidx.core:core:1.6.0 -> 1.15.0 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|    |    |    |    +--- androidx.core:core:1.1.0 -> 1.15.0 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |    |    |    +--- androidx.interpolator:interpolator:1.0.0 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|    |    |    \--- androidx.appcompat:appcompat:1.6.1 (c)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.4.2 (*)
|    |    +--- androidx.core:core:1.9.0 -> 1.15.0 (*)
|    |    +--- androidx.core:core-ktx:1.8.0 -> 1.12.0 (*)
|    |    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    |    +--- androidx.core:core:1.0.0 -> 1.15.0 (*)
|    |    |    \--- androidx.customview:customview:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.emoji2:emoji2:1.2.0
|    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.9.1 (*)
|    |    |    +--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|    |    |    +--- androidx.core:core:1.3.0 -> 1.15.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-process:2.4.1 -> 2.8.7 (*)
|    |    |    \--- androidx.startup:startup-runtime:1.0.0 -> 1.1.1 (*)
|    |    +--- androidx.emoji2:emoji2-views-helper:1.2.0
|    |    |    +--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|    |    |    +--- androidx.core:core:1.3.0 -> 1.15.0 (*)
|    |    |    \--- androidx.emoji2:emoji2:1.2.0 (*)
|    |    +--- androidx.fragment:fragment:1.3.6 -> 1.7.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7 (*)
|    |    +--- androidx.resourceinspection:resourceinspection-annotation:1.0.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 1.9.24 (*)
|    |    \--- androidx.appcompat:appcompat-resources:1.6.1 (c)
|    +--- com.google.zxing:core:3.5.2
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :shared_preferences_android
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0 (*)
|    +--- androidx.datastore:datastore:1.1.3
|    |    \--- androidx.datastore:datastore-android:1.1.3
|    |         +--- androidx.annotation:annotation:1.2.0 -> 1.9.1 (*)
|    |         +--- androidx.datastore:datastore-core:1.1.3
|    |         |    \--- androidx.datastore:datastore-core-android:1.1.3
|    |         |         +--- androidx.annotation:annotation:1.7.0 -> 1.9.1 (*)
|    |         |         +--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22
|    |         |         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.22 -> 1.9.24 (*)
|    |         |         |    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22
|    |         |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.22 -> 1.9.24 (*)
|    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 (*)
|    |         |         +--- androidx.datastore:datastore:1.1.3 (c)
|    |         |         +--- androidx.datastore:datastore-core-okio:1.1.3 (c)
|    |         |         +--- androidx.datastore:datastore-preferences:1.1.3 (c)
|    |         |         +--- androidx.datastore:datastore-preferences-core:1.1.3 (c)
|    |         |         +--- androidx.datastore:datastore-preferences-proto:1.1.3 (c)
|    |         |         \--- androidx.datastore:datastore-preferences-external-protobuf:1.1.3 (c)
|    |         +--- androidx.datastore:datastore-core-okio:1.1.3
|    |         |    \--- androidx.datastore:datastore-core-okio-jvm:1.1.3
|    |         |         +--- androidx.datastore:datastore-core:1.1.3 (*)
|    |         |         +--- com.squareup.okio:okio:3.4.0
|    |         |         |    \--- com.squareup.okio:okio-jvm:3.4.0
|    |         |         |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 1.9.0 (*)
|    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 (*)
|    |         |         +--- androidx.datastore:datastore:1.1.3 (c)
|    |         |         +--- androidx.datastore:datastore-core:1.1.3 (c)
|    |         |         +--- androidx.datastore:datastore-preferences:1.1.3 (c)
|    |         |         +--- androidx.datastore:datastore-preferences-core:1.1.3 (c)
|    |         |         +--- androidx.datastore:datastore-preferences-proto:1.1.3 (c)
|    |         |         \--- androidx.datastore:datastore-preferences-external-protobuf:1.1.3 (c)
|    |         +--- com.squareup.okio:okio:3.4.0 (*)
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 (*)
|    |         +--- androidx.datastore:datastore-core:1.1.3 (c)
|    |         +--- androidx.datastore:datastore-core-okio:1.1.3 (c)
|    |         +--- androidx.datastore:datastore-preferences:1.1.3 (c)
|    |         +--- androidx.datastore:datastore-preferences-core:1.1.3 (c)
|    |         +--- androidx.datastore:datastore-preferences-proto:1.1.3 (c)
|    |         \--- androidx.datastore:datastore-preferences-external-protobuf:1.1.3 (c)
|    +--- androidx.datastore:datastore-preferences:1.1.3
|    |    \--- androidx.datastore:datastore-preferences-android:1.1.3
|    |         +--- androidx.datastore:datastore:1.1.3 (*)
|    |         +--- androidx.datastore:datastore-preferences-core:1.1.3
|    |         |    \--- androidx.datastore:datastore-preferences-core-jvm:1.1.3
|    |         |         +--- androidx.datastore:datastore-core:1.1.3 (*)
|    |         |         +--- androidx.datastore:datastore-core-okio:1.1.3 (*)
|    |         |         +--- androidx.datastore:datastore-preferences-proto:1.1.3
|    |         |         |    +--- androidx.datastore:datastore-preferences-external-protobuf:1.1.3
|    |         |         |    |    +--- androidx.datastore:datastore:1.1.3 (c)
|    |         |         |    |    +--- androidx.datastore:datastore-core:1.1.3 (c)
|    |         |         |    |    +--- androidx.datastore:datastore-core-okio:1.1.3 (c)
|    |         |         |    |    +--- androidx.datastore:datastore-preferences:1.1.3 (c)
|    |         |         |    |    +--- androidx.datastore:datastore-preferences-core:1.1.3 (c)
|    |         |         |    |    \--- androidx.datastore:datastore-preferences-proto:1.1.3 (c)
|    |         |         |    +--- androidx.datastore:datastore:1.1.3 (c)
|    |         |         |    +--- androidx.datastore:datastore-core:1.1.3 (c)
|    |         |         |    +--- androidx.datastore:datastore-core-okio:1.1.3 (c)
|    |         |         |    +--- androidx.datastore:datastore-preferences:1.1.3 (c)
|    |         |         |    +--- androidx.datastore:datastore-preferences-core:1.1.3 (c)
|    |         |         |    \--- androidx.datastore:datastore-preferences-external-protobuf:1.1.3 (c)
|    |         |         +--- com.squareup.okio:okio:3.4.0 (*)
|    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |         |         +--- androidx.datastore:datastore:1.1.3 (c)
|    |         |         +--- androidx.datastore:datastore-core:1.1.3 (c)
|    |         |         +--- androidx.datastore:datastore-core-okio:1.1.3 (c)
|    |         |         +--- androidx.datastore:datastore-preferences:1.1.3 (c)
|    |         |         +--- androidx.datastore:datastore-preferences-proto:1.1.3 (c)
|    |         |         \--- androidx.datastore:datastore-preferences-external-protobuf:1.1.3 (c)
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 (*)
|    |         +--- androidx.datastore:datastore:1.1.3 (c)
|    |         +--- androidx.datastore:datastore-core:1.1.3 (c)
|    |         +--- androidx.datastore:datastore-core-okio:1.1.3 (c)
|    |         +--- androidx.datastore:datastore-preferences-core:1.1.3 (c)
|    |         +--- androidx.datastore:datastore-preferences-proto:1.1.3 (c)
|    |         \--- androidx.datastore:datastore-preferences-external-protobuf:1.1.3 (c)
|    +--- androidx.preference:preference:1.2.1
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.9.1 (*)
|    |    +--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
|    |    +--- androidx.core:core:1.6.0 -> 1.15.0 (*)
|    |    +--- androidx.activity:activity-ktx:1.5.1 -> 1.8.1
|    |    |    +--- androidx.activity:activity:1.8.1 (*)
|    |    |    +--- androidx.core:core-ktx:1.9.0 -> 1.12.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 -> 2.8.7
|    |    |    |    \--- androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7
|    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.9.1 (*)
|    |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (*)
|    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 (*)
|    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-common-java8:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-service:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7 (c)
|    |    |    |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 -> 2.8.7
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (*)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common-java8:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-service:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-runtime-ktx:2.8.7 (c)
|    |    |    +--- androidx.savedstate:savedstate-ktx:1.2.1
|    |    |    |    +--- androidx.savedstate:savedstate:1.2.1 (*)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|    |    |    |    \--- androidx.savedstate:savedstate:1.2.1 (c)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    |    \--- androidx.activity:activity:1.8.1 (c)
|    |    +--- androidx.fragment:fragment-ktx:1.3.6 -> 1.7.1
|    |    |    +--- androidx.activity:activity-ktx:1.8.1 (*)
|    |    |    +--- androidx.collection:collection-ktx:1.1.0 -> 1.4.2
|    |    |    |    +--- androidx.collection:collection:1.4.2 (*)
|    |    |    |    \--- androidx.collection:collection:1.4.2 (c)
|    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.12.0 (*)
|    |    |    +--- androidx.fragment:fragment:1.7.1 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 -> 2.8.7 (*)
|    |    |    +--- androidx.savedstate:savedstate-ktx:1.2.1 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 1.9.24 (*)
|    |    |    \--- androidx.fragment:fragment:1.7.1 (c)
|    |    +--- androidx.recyclerview:recyclerview:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    |    +--- androidx.core:core:1.0.0 -> 1.15.0 (*)
|    |    |    \--- androidx.legacy:legacy-support-core-ui:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    |         +--- androidx.core:core:1.0.0 -> 1.15.0 (*)
|    |    |         +--- androidx.legacy:legacy-support-core-utils:1.0.0
|    |    |         |    +--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    |         |    +--- androidx.core:core:1.0.0 -> 1.15.0 (*)
|    |    |         |    +--- androidx.documentfile:documentfile:1.0.0
|    |    |         |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    |         |    +--- androidx.loader:loader:1.0.0 (*)
|    |    |         |    +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|    |    |         |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    |         |    \--- androidx.print:print:1.0.0
|    |    |         |         \--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    |         +--- androidx.customview:customview:1.0.0 -> 1.1.0 (*)
|    |    |         +--- androidx.viewpager:viewpager:1.0.0 (*)
|    |    |         +--- androidx.coordinatorlayout:coordinatorlayout:1.0.0
|    |    |         |    +--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    |         |    +--- androidx.core:core:1.0.0 -> 1.15.0 (*)
|    |    |         |    \--- androidx.customview:customview:1.0.0 -> 1.1.0 (*)
|    |    |         +--- androidx.drawerlayout:drawerlayout:1.0.0 (*)
|    |    |         +--- androidx.slidingpanelayout:slidingpanelayout:1.0.0 -> 1.2.0
|    |    |         |    +--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|    |    |         |    +--- androidx.customview:customview:1.1.0 (*)
|    |    |         |    +--- androidx.core:core:1.1.0 -> 1.15.0 (*)
|    |    |         |    +--- androidx.window:window:1.0.0 -> 1.2.0 (*)
|    |    |         |    \--- androidx.transition:transition:1.4.1
|    |    |         |         +--- androidx.annotation:annotation:1.1.0 -> 1.9.1 (*)
|    |    |         |         +--- androidx.core:core:1.1.0 -> 1.15.0 (*)
|    |    |         |         \--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|    |    |         +--- androidx.interpolator:interpolator:1.0.0 (*)
|    |    |         +--- androidx.swiperefreshlayout:swiperefreshlayout:1.0.0
|    |    |         |    +--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    |         |    +--- androidx.core:core:1.0.0 -> 1.15.0 (*)
|    |    |         |    \--- androidx.interpolator:interpolator:1.0.0 (*)
|    |    |         +--- androidx.asynclayoutinflater:asynclayoutinflater:1.0.0
|    |    |         |    +--- androidx.annotation:annotation:1.0.0 -> 1.9.1 (*)
|    |    |         |    \--- androidx.core:core:1.0.0 -> 1.15.0 (*)
|    |    |         \--- androidx.cursoradapter:cursoradapter:1.0.0 (*)
|    |    +--- androidx.slidingpanelayout:slidingpanelayout:1.2.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.4.2 (*)
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :sqflite_android
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :sqlite3_flutter_libs
|    +--- eu.simonbinder:sqlite3-native-library:3.49.2
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
+--- project :url_launcher_android
|    +--- androidx.core:core:1.13.1 -> 1.15.0 (*)
|    +--- androidx.annotation:annotation:1.9.1 (*)
|    +--- androidx.browser:browser:1.8.0
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.9.1 (*)
|    |    +--- androidx.annotation:annotation-experimental:1.4.0 -> 1.4.1 (*)
|    |    +--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.15.0 (*)
|    |    +--- androidx.interpolator:interpolator:1.0.0 (*)
|    |    \--- com.google.guava:listenablefuture:1.0
|    \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)
\--- project :wakelock_plus
     +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.22 -> 1.9.24 (*)
     +--- project :package_info_plus (*)
     \--- io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7 (*)

(c) - A dependency constraint, not a dependency. The dependency affected by the constraint occurs elsewhere in the tree.
(*) - Indicates repeated occurrences of a transitive dependency subtree. Gradle expands transitive dependency subtrees only once per project; repeat occurrences only display the root of the subtree, followed by this annotation.

A web-based, searchable dependency report is available by adding the --scan option.

BUILD SUCCESSFUL in 1s
6 actionable tasks: 2 executed, 4 up-to-date
