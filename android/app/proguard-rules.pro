# App Usage Plugin
-keep class dk.cachet.app_usage.** { *; }
-keepclassmembers class dk.cachet.app_usage.** { *; }

# Google Play Services - Only for non-F-Droid builds
# F-Droid builds exclude these to avoid Google Play dependencies
-keep class com.google.android.gms.** { *; }

# F-Droid specific rules to exclude Google Play Core dependencies
# These rules aggressively remove Google Play Core classes that F-Droid detects
-assumenosideeffects class com.google.android.play.core.** { *; }
-dontwarn com.google.android.play.core.**

# Remove Google Play Services for F-Droid builds (commented out for regular builds)
# -assumenosideeffects class com.google.android.gms.** { *; }
# -dontwarn com.google.android.gms.**

# Aggressively remove the specific problematic classes detected by F-Droid
-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallSessionState { *; }
-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallManager { *; }
-assumenosideeffects class com.google.android.play.core.tasks.OnFailureListener { *; }
-assumenosideeffects class com.google.android.play.core.splitcompat.SplitCompatApplication { *; }
-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener { *; }
-assumenosideeffects class com.google.android.play.core.tasks.OnSuccessListener { *; }

# Remove all references and warnings for Google Play Core packages
-dontwarn com.google.android.play.core.splitinstall.**
-dontwarn com.google.android.play.core.tasks.**
-dontwarn com.google.android.play.core.splitcompat.**
-dontwarn com.google.android.play.core.appupdate.**
-dontwarn com.google.android.play.core.review.**
-dontwarn com.google.android.play.core.common.**

# Remove any remaining Google Play references
-dontwarn com.google.android.play.**
-assumenosideeffects class com.google.android.play.** { *; }

# Flutter optimizations
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Aggressive optimizations
-allowaccessmodification
-mergeinterfacesaggressively
-optimizationpasses 5
-overloadaggressively
-repackageclasses ''

# Remove unused code
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Dart/Flutter specific optimizations
-dontwarn io.flutter.embedding.**
-dontwarn io.flutter.plugin.**

# Database optimizations
-keep class org.sqlite.** { *; }
-keep class androidx.sqlite.** { *; }

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Remove debug information
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable
