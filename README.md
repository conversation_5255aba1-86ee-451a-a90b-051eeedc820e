# ![Software logo](https://github.com/user-attachments/assets/f58599ea-3313-49ec-a01c-31436340a2a3) WHPH - Work Hard Play Hard [![Buy Me A Coffee](https://img.shields.io/badge/Buy%20Me%20a%20Coffee-ffdd00?&logo=buy-me-a-coffee&logoColor=black)](https://ahmetcetinkaya.me/donate) ![GitHub stars](https://img.shields.io/github/stars/ahmet-cetinkaya/whph?style=social) ![GitHub license](https://img.shields.io/github/license/ahmet-cetinkaya/whph)

WHPH is a comprehensive productivity app designed to help you manage tasks, develop new habits, and optimize your time. It tracks application usage to enhance focus, allowing you to analyze your progress, celebrate achievements, and stay motivated.

Available for Android, Windows, and Linux platforms.

![Android](https://img.shields.io/badge/Android-3DDC84?style=for-the-badge&logo=android&logoColor=white) ![Windows](https://img.shields.io/badge/Windows-0078D6?style=for-the-badge&logo=windows&logoColor=white) ![Linux](https://img.shields.io/badge/Linux-FCC624?style=for-the-badge&logo=linux&logoColor=black)

## ⏬ Installation

Download the latest version of the app from the [releases page](https://github.com/ahmet-cetinkaya/whph/releases).

[![GitHub Release](https://img.shields.io/github/v/release/ahmet-cetinkaya/whph?label=Latest%20Release&style=for-the-badge&logo=github)](https://github.com/ahmet-cetinkaya/whph/releases)


## 📱 Screenshots

<p align="center">
  <a href="docs/screenshots/mobile_01.png"><img src="docs/screenshots/mobile_01.png" alt="Mobile 01" width="220"/></a>
  <a href="docs/screenshots/mobile_02.png"><img src="docs/screenshots/mobile_02.png" alt="Mobile 02" width="220"/></a>
  <a href="docs/screenshots/mobile_03.png"><img src="docs/screenshots/mobile_03.png" alt="Mobile 03" width="220"/></a>
  <a href="docs/screenshots/mobile_04.png"><img src="docs/screenshots/mobile_04.png" alt="Mobile 04" width="220"/></a>
  <a href="docs/screenshots/mobile_05.png"><img src="docs/screenshots/mobile_05.png" alt="Mobile 05" width="220"/></a>
  <a href="docs/screenshots/mobile_06.png"><img src="docs/screenshots/mobile_06.png" alt="Mobile 06" width="220"/></a>
  <a href="docs/screenshots/mobile_07.png"><img src="docs/screenshots/mobile_07.png" alt="Mobile 07" width="220"/></a>
  <a href="docs/screenshots/mobile_08.png"><img src="docs/screenshots/mobile_08.png" alt="Mobile 08" width="220"/></a>
  <a href="docs/screenshots/mobile_09.png"><img src="docs/screenshots/mobile_09.png" alt="Mobile 09" width="220"/></a>
  <a href="docs/screenshots/mobile_10.png"><img src="docs/screenshots/mobile_10.png" alt="Mobile 10" width="220"/></a>
</p>

## 🤝 Contributing

If you'd like to contribute, please follow these steps:

1. Fork the repository
2. Create a new branch (e.g., `git checkout -b feat/feature-branch`)
3. Make your changes
4. Commit your changes (e.g., `git commit -m 'feat: add new feature'`)
5. Push to the branch (e.g., `git push origin feat/feature-branch`)
6. Open a Pull Request

### 📋 Requirements

- Flutter SDK
- Dart SDK
- A compatible device or emulator for Windows, Linux, or Android

### ⚙️ Running the Project

1. Clone the repository (with submodules):
   
   ```bash
   git clone --recurse-submodules https://github.com/ahmet-cetinkaya/whph.git
   ```
   
   If you already cloned without `--recurse-submodules`, run:
   
   ```bash
   git submodule update --init --recursive
   ```

2. Navigate into the project directory:
   
   ```bash
   cd whph
   ```

3. Install the dependencies:
   
   ```bash
   flutter pub get
   ```

4. Start the application:
   
   ```bash
   flutter run
   ```

### 🔒 Security & Reproducible Builds

WHPH implements security best practices and supports reproducible builds:

#### Quick Commands (with RPS)
```bash
# Install RPS globally (one-time setup)
dart pub global activate rps

# Security validation
rps security-check

# Build production APK with security validation
rps release:android

# Build fully reproducible APK
rps release:android:reproducible

# Build Android App Bundle for Play Store
rps release:android:bundle
```

#### Manual Commands
```bash
# Security validation
bash scripts/security_validation.sh

# Reproducible build
flutter clean && flutter pub get && bash scripts/security_validation.sh && flutter build apk --release --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons
```

For detailed build commands and workflows, see [Build Commands Reference](docs/BUILD_COMMANDS.md)

For information about security measures and reproducible builds, see [Reproducible Build Guide](docs/REPRODUCIBLE_BUILD.md)