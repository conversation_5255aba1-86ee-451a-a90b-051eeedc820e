#!/bin/bash

# Version bump script for WHPH project
# Updates version in pubspec.yaml, app_info.dart, installer.iss files and generates changelog

set -e

# Check if argument is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 [major|minor|patch]"
    exit 1
fi

BUMP_TYPE=$1

# Validate bump type
if [[ "$BUMP_TYPE" != "major" && "$BUMP_TYPE" != "minor" && "$BUMP_TYPE" != "patch" ]]; then
    echo "Error: Invalid bump type. Use 'major', 'minor', or 'patch'"
    exit 1
fi

# Get project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# File paths
PUBSPEC_FILE="$PROJECT_ROOT/pubspec.yaml"
APP_INFO_FILE="$PROJECT_ROOT/lib/src/core/domain/shared/constants/app_info.dart"
INSTALLER_FILE="$PROJECT_ROOT/windows/setup-wizard/installer.iss"
FDROID_METADATA_FILE="$PROJECT_ROOT/android/fdroid/metadata/me.ahmetcetinkaya.whph.yml"

# Extract current version from pubspec.yaml
CURRENT_VERSION=$(grep "^version:" "$PUBSPEC_FILE" | sed 's/version: //' | sed 's/+.*//')

echo "Current version: $CURRENT_VERSION"

# Split version into components
IFS='.' read -r -a VERSION_PARTS <<< "$CURRENT_VERSION"
MAJOR=${VERSION_PARTS[0]}
MINOR=${VERSION_PARTS[1]}
PATCH=${VERSION_PARTS[2]}

echo "Current: $MAJOR.$MINOR.$PATCH"

# Bump version based on type
case $BUMP_TYPE in
    "major")
        MAJOR=$((MAJOR + 1))
        MINOR=0
        PATCH=0
        ;;
    "minor")
        MINOR=$((MINOR + 1))
        PATCH=0
        ;;
    "patch")
        PATCH=$((PATCH + 1))
        ;;
esac

NEW_VERSION="$MAJOR.$MINOR.$PATCH"
echo "New version: $NEW_VERSION"

# Get current build number from pubspec.yaml
CURRENT_BUILD=$(grep "^version:" "$PUBSPEC_FILE" | sed 's/.*+//')
NEW_BUILD=$((CURRENT_BUILD + 1))

# Update pubspec.yaml
echo "Updating $PUBSPEC_FILE..."
sed -i "s/^version:.*/version: $NEW_VERSION+$NEW_BUILD/" "$PUBSPEC_FILE"

# Update app_info.dart
echo "Updating $APP_INFO_FILE..."
sed -i "s/static const String version = \".*\";/static const String version = \"$NEW_VERSION\";/" "$APP_INFO_FILE"

# Update installer.iss
echo "Updating $INSTALLER_FILE..."
sed -i "s/AppVersion=.*/AppVersion=$NEW_VERSION/" "$INSTALLER_FILE"

# First update F-Droid metadata without commit hash
echo "Updating $FDROID_METADATA_FILE (initial)..."
# Update all three architecture builds with incremental version codes
sed -i "s/versionName: .*/versionName: $NEW_VERSION/g" "$FDROID_METADATA_FILE"

# Update version codes for each architecture specifically
# x86_64 (first occurrence)
sed -i "0,/versionCode: .*/{s/versionCode: .*/versionCode: $((NEW_BUILD * 10 + 1))/}" "$FDROID_METADATA_FILE"
# armeabi-v7a (second occurrence) 
awk -v new_code="$((NEW_BUILD * 10 + 2))" '
/versionCode: / && ++count == 2 {
    sub(/versionCode: .*/, "versionCode: " new_code)
}
{print}
' "$FDROID_METADATA_FILE" > "$FDROID_METADATA_FILE.tmp" && mv "$FDROID_METADATA_FILE.tmp" "$FDROID_METADATA_FILE"
# arm64-v8a (third occurrence)
awk -v new_code="$((NEW_BUILD * 10 + 3))" '
/versionCode: / && ++count == 3 {
    sub(/versionCode: .*/, "versionCode: " new_code)
}
{print}
' "$FDROID_METADATA_FILE" > "$FDROID_METADATA_FILE.tmp" && mv "$FDROID_METADATA_FILE.tmp" "$FDROID_METADATA_FILE"

# Update VERSION_CODE environment variables in build sections
# x86_64 (first occurrence)
sed -i "0,/export VERSION_CODE=.*/{s/export VERSION_CODE=.*/export VERSION_CODE=$((NEW_BUILD * 10 + 1))/}" "$FDROID_METADATA_FILE"
# armeabi-v7a (second occurrence)
awk -v new_code="$((NEW_BUILD * 10 + 2))" '
/export VERSION_CODE=/ && ++count == 2 {
    sub(/export VERSION_CODE=.*/, "export VERSION_CODE=" new_code)
}
{print}
' "$FDROID_METADATA_FILE" > "$FDROID_METADATA_FILE.tmp" && mv "$FDROID_METADATA_FILE.tmp" "$FDROID_METADATA_FILE"
# arm64-v8a (third occurrence)
awk -v new_code="$((NEW_BUILD * 10 + 3))" '
/export VERSION_CODE=/ && ++count == 3 {
    sub(/export VERSION_CODE=.*/, "export VERSION_CODE=" new_code)
}
{print}
' "$FDROID_METADATA_FILE" > "$FDROID_METADATA_FILE.tmp" && mv "$FDROID_METADATA_FILE.tmp" "$FDROID_METADATA_FILE"

sed -i "s/CurrentVersion: .*/CurrentVersion: $NEW_VERSION+$NEW_BUILD/" "$FDROID_METADATA_FILE"
sed -i "s/CurrentVersionCode: .*/CurrentVersionCode: $((NEW_BUILD * 10 + 3))/" "$FDROID_METADATA_FILE"

# Generate changelog
echo "Generating changelog..."
cd "$PROJECT_ROOT"
bash scripts/create_changelog.sh "$NEW_BUILD" --auto

# Git operations - Create version bump commit first
echo "Creating version bump commit..."

# First, stage changes in the F-Droid submodule
echo "Staging F-Droid metadata changes in submodule..."
cd "$PROJECT_ROOT/android/fdroid"
git add "metadata/me.ahmetcetinkaya.whph.yml"

echo ""
echo "About to commit F-Droid metadata changes with message:"
echo "  feat(me.ahmetcetinkaya.whph): update app version to $NEW_VERSION"
echo ""
read -p "Do you want to proceed with this commit? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git commit -m "feat(me.ahmetcetinkaya.whph): update app version to $NEW_VERSION"
    echo "✓ F-Droid metadata commit created"
else
    echo "❌ F-Droid metadata commit cancelled"
    exit 1
fi

cd "$PROJECT_ROOT"

# Then, stage changes in the main repository (including submodule update)
echo "Staging main repository changes..."
git add "$PUBSPEC_FILE" "$APP_INFO_FILE" "$INSTALLER_FILE" "android/fdroid" "CHANGELOG.md" "fastlane/metadata/android/en-US/changelogs/"

echo ""
echo "About to commit main repository changes with message:"
echo "  chore: update app version to $NEW_VERSION"
echo ""
read -p "Do you want to proceed with this commit? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git commit -m "chore: update app version to $NEW_VERSION"
    echo "✓ Main repository commit created"
else
    echo "❌ Main repository commit cancelled"
    exit 1
fi

# Get the commit hash of the version bump commit
VERSION_COMMIT=$(git rev-parse HEAD)

# Now update F-Droid metadata with the correct commit hash
echo "Updating F-Droid metadata with commit hash..."
cd "$PROJECT_ROOT/android/fdroid"
# Update all three architecture builds with the same commit hash
sed -i "s/commit: .*/commit: $VERSION_COMMIT/g" "metadata/me.ahmetcetinkaya.whph.yml"
git add "metadata/me.ahmetcetinkaya.whph.yml"

echo ""
echo "About to commit F-Droid metadata with correct commit hash:"
echo "  build(me.ahmetcetinkaya.whph): update commit hash for F-Droid build"
echo "  Commit hash: $VERSION_COMMIT"
echo ""
read -p "Do you want to proceed with this commit? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git commit -m "build(me.ahmetcetinkaya.whph): update commit hash for F-Droid build"
    echo "✓ F-Droid commit hash update completed"
else
    echo "❌ F-Droid commit hash update cancelled"
    exit 1
fi

cd "$PROJECT_ROOT"

# Update the submodule reference in main repo
git add "android/fdroid"

echo ""
echo "About to commit submodule update with message:"
echo "  chore: update F-Droid submodule with commit hash"
echo ""
read -p "Do you want to proceed with this commit? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git commit -m "chore: update F-Droid submodule with commit hash"
    echo "✓ Submodule update commit created"
else
    echo "❌ Submodule update commit cancelled"
    exit 1
fi

# Create version tag
echo ""
echo "About to create version tag:"
echo "  Tag: v$NEW_VERSION"
echo "  Message: Version $NEW_VERSION"
echo ""
read -p "Do you want to create this tag? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git tag -a "v$NEW_VERSION" -m "Version $NEW_VERSION"
    echo "✓ Version tag created: v$NEW_VERSION"
else
    echo "❌ Version tag creation cancelled"
    exit 1
fi

echo "Version bump completed successfully!"
echo "Updated files:"
echo "  - $PUBSPEC_FILE (version: $NEW_VERSION+$NEW_BUILD)"
echo "  - $APP_INFO_FILE (version: $NEW_VERSION)"
echo "  - $INSTALLER_FILE (version: $NEW_VERSION)"
echo "  - $FDROID_METADATA_FILE (versionName: $NEW_VERSION, versionCodes: $((NEW_BUILD * 10 + 1)), $((NEW_BUILD * 10 + 2)), $((NEW_BUILD * 10 + 3)), commit: $VERSION_COMMIT)"
echo "  - CHANGELOG.md (generated for version $NEW_VERSION)"
echo "  - fastlane/metadata/android/en-US/changelogs/ (generated for version code $NEW_BUILD)"
echo ""

# Git operations completed
echo "Git operations completed:"
echo "  - Created version bump commit: $VERSION_COMMIT"
echo "  - Updated F-Droid metadata with correct commit hash"
echo "  - Created version tag: v$NEW_VERSION"
echo ""
echo "To push changes and tags to remote:"
echo "  rps version:push"
echo ""
echo "This will:"
echo "  1. Push F-Droid submodule changes"
echo "  2. Push main repository changes and tags"
