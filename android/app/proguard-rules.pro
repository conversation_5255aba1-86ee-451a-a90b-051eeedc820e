# App Usage Plugin
-keep class dk.cachet.app_usage.** { *; }
-keepclassmembers class dk.cachet.app_usage.** { *; }

# Google Play Services - Only for non-F-Droid builds
# F-Droid builds exclude these to avoid Google Play dependencies
-keep class com.google.android.gms.** { *; }

# Flutter optimizations
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Aggressive optimizations
-allowaccessmodification
-mergeinterfacesaggressively
-optimizationpasses 5
-overloadaggressively
-repackageclasses ''

# Remove unused code
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Dart/Flutter specific optimizations
-dontwarn io.flutter.embedding.**
-dontwarn io.flutter.plugin.**

# Database optimizations
-keep class org.sqlite.** { *; }
-keep class androidx.sqlite.** { *; }

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Remove debug information
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable
